import { Wallpaper } from "@/types/wallpaper";
import { getSupabaseClient } from "./db";
import { promises } from "dns";

export async function insertWallpaper(wallpapers: Wallpaper[]) {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase.from("wallpapers").insert(wallpapers);

  if (error) throw error;

  return data;
}

export async function getWallpapers(
  page: number = 1,
  limit: number = 50
): Promise<Wallpaper[] | undefined> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("wallpapers")
    .select("*")
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) throw error;

  return data;
}

export async function getOnlineWallpapers(
  page: number = 1,
  limit: number = 50
): Promise<Wallpaper[] | undefined> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("wallpapers")
    .select("*")
    .eq("status", "online")
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) throw error;

  return data;
}

export async function findWallpaperByUuid(uuid: string): Promise<Wallpaper | undefined> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("wallpapers")
    .select("*")
    .eq("uuid", uuid);

  if (error) throw error;

  return data[0];
}


export async function updateWallpaper(
  uuid: string,
  wallpaper: Partial<Wallpaper>
) {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("wallpapers")
    .update(wallpaper)
    .eq("uuid", uuid);

  if (error) throw error;

  return data;
}