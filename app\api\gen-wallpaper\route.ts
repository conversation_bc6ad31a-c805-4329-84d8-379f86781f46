import { respData, respErr } from "@/lib/resp";
import { experimental_generateImage as generateImage } from "ai";
import { replicate } from "@ai-sdk/replicate";
import { getUuid } from "@/lib/uuid";
import path from "path";
import { writeFile } from "fs/promises";
import { Wallpaper } from "@/types/wallpaper";  
import {insertWallpaper } from "@/models/wallpaper";
import { getIsoTimestr } from "@/lib/time";
import { decreaseCredits } from "@/services/credit";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    const { description } = await req.json();
    const prompt = `generate a wallpaper with the following description: ${description}`;

    
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
        return respErr("user not login");
  }

    
    const credits = await getUserCredits(user_uuid);
    if (credits.left_credits < cost_credits) {
       return respErr("credits not enough, please recharge");
   }

    const model = "black-forest-labs/flux-schnell";
    
    const imageModel = replicate.image(model);
    const providerOptions = {
      replicate: {
    output_quality: 90,
      },
    };
 
   const { images, warnings } = await generateImage({
     model: imageModel,
     prompt: prompt,
     n: 1,
     providerOptions,
     aspectRatio: "16:9",
   });
   
   if (warnings.length > 0) {
     throw new Error("generate wallpaper failed: " + JSON.stringify(warnings));
   }
   if (!images || images.length === 0) {
     throw new Error("generate wallpaper failed: no images returned");
   }
 
const batch = getUuid();
const provider = "replicate"; 
const wallpapers = await Promise.all(
  images.map(async (image, index) => {
    const fileName = `${provider}_image_${batch}_${index}.png`;
    const filePath = path.join(process.cwd(), "public", fileName);
    const url = `${process.env.NEXT_PUBLIC_WEB_URL}/${fileName}`;
    const buffer = Buffer.from(image.base64, "base64");
    await writeFile(filePath, buffer);
 
    return {
        uuid:getUuid(),
        img_description: description,
        image_url: url,
        status: "created",
        created_at: getIsoTimestr(),
    }   as Wallpaper;
    
  })
);
   await insertWallpaper(wallpapers);

   await decreaseCredits({
    user_uuid,
    trans_type: CreditsTransType.GenWallpaper,
    credits: CreditsAmount.GenWallpaperCost,
    });

    return respData({
      prompt: prompt,
      wallpapers: wallpapers,
      credits: credits.left_credits,
    });
  } catch (e) {
    console.log("generate wallpaper failed:", e);
    return respErr("generate wallpaper failed");
  }
}