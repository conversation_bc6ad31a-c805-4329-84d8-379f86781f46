import { Wallpaper } from "@/types/wallpaper";

export default function Wallpapers({
  wallpapers,
}: {
  wallpapers: Wallpaper[] | undefined;
}) {
  // 定义图片卡片组件，接收单个 wallpaper 数据
  function ImageCard({ wallpaper }: { wallpaper: Wallpaper }) {
    return (
      <div className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
        <div className="relative aspect-[1.75] overflow-hidden">
          <img
            src={wallpaper.image_url}
            alt={wallpaper.img_description}
            className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
          />
        </div>
        <div className="p-4">
          <div className="flex justify-between items-center">
            <h3 className="text-gray-800 font-medium text-sm">
              {wallpaper.img_description}
            </h3>
            <div className="flex items-center gap-2">
              <span className="text-gray-500 text-xs">
                {wallpaper.created_at?.toString()}
              </span>
              <div
                className="w-6 h-6 rounded-full text-white flex items-center justify-center"
              >
                {wallpaper.user_uuid}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {wallpapers?.map((wallpaper) => (
          <ImageCard key={wallpaper.uuid} wallpaper={wallpaper} />
        ))}
      </div>
    </div>
  );
}