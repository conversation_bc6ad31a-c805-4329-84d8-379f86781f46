import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getUsers } from "@/models/user";
import moment from "moment";
import { getWallpapers } from "@/models/wallpaper";

export default async function () {
  const wallpapers = await getwallpapers(1, 50);

  const columns: TableColumn[] = [
    { name: "uuid", title: "UUID" },
    { name: "img_description", title: "Description" },
    { name: "img_url", title: "URL" },
    {
      name: "status",
      title: "Status",
      callback: (row) => (
        <img src={row.avatar_url} className="w-10 h-10 rounded-full" />
      ),
    },
    {
      name: "created_at",
      title: "Created At",
      callback: (row) => moment(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
    },
  ];

  const table: TableSlotType = {
    title: "All Wallpapers",
    columns,
    data: wallpapers,
  };

  return <TableSlot {...table} />;
}
