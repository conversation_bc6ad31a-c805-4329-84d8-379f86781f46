import { TableColumn } from "@/types/blocks/table";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getUsers } from "@/models/user";
import moment from "moment";
import { getWallpapers } from "@/models/wallpaper";

export default async function () {
  const wallpapers = await getWallpapers(1, 50);

  const columns: TableColumn[] = [
    { name: "uuid", title: "UUID" },
    { name: "img_description", title: "Description" },
    { name: "image_url", title: "URL", callback: (row) => (
       <a href={row.image_url} target="_blank"> 
        <img src={row.image_url} className="w-40 h-20 rounded-md" />
         </a>
    )},
    {
      name: "status",
      title: "Status",
      callback: (row) => (
        <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
          {row.status}
        </span>
      ),
    },
    {
      name: "created_at",
      title: "Created At",
      callback: (row) => moment(row.created_at).format("YYYY-MM-DD HH:mm:ss"),
    },
  ];

  const table: TableSlotType = {
    title: "All Wallpapers",
    columns,
    data: wallpapers,
  };

  return <TableSlot {...table} />;
}
