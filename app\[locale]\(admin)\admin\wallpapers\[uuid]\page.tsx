
import Empty from "@/components/blocks/empty";
import FormSlot from "@/components/dashboard/slots/form";
import { Form as FormSlotType } from "@/types/slots/form";
import { Post } from "@/types/post";
import { getIsoTimestr } from "@/lib/time";
import { getUserInfo } from "@/services/user";
import { findWallpaperByUuid } from "@/models/wallpaper";

export default async function ({
  params,
}: {
  params: Promise<{ uuid: string }>;
}) {
  const { uuid } = await params;
  const user = await getUserInfo();
  if (!user || !user.uuid) {
    return <Empty message="no auth" />;
  }

  const post = await findWallpaperByUuid(params.uuid);
  if (!wallpaper) {
    return <Empty message="wallpaper not found" />;
  }

  const form: FormSlotType = {
    title: "Edit Wallpapers",
    crumb: {
      items: [
        {
          title: "Wallpapers",
          url: "/admin/Wallpapers",
        },
        {
          title: "Edit Wallpapers",
          is_active: true,
        },
      ],
    },
    fields: [
      {
        name: "image_url",
        title: "Image URL",
        type: "text",
        placeholder: "Image URL",
        validation: {
          required: true,
        },
      },
      {
        name: "img_description",
        title: "Image Description",
        type: "text",
        placeholder: "Image Description",
        validation: {
          required: true,
        },
        tip: "wallpaper description",
      },
      {
        name: "status",
        title: "Status",
        type: "select",
        options: [
            
          title: Online,
          value: Online,
        },
        {
          title: "Offline",
          value: "offline",
        },
        {
          title: "Created",
          value: "created",
        },
        
       
    ],
    data: wallpaper,
    passby: {
      user,
      wallpaper,
    },
    submit: {
      button: {
        title: "Submit",
      },
      handler: async (data: FormData, passby: any) => {
        "use server";

        const { user, wallpaper } = passby;
        if (!user || !wallpaper || !wallpaper.uuid) {
          throw new Error("invalid params");
        }

        
        const status = data.get("status") as string;
        const description = data.get("description") as string;
        

        if (!image_url || image_url.trim()) {
           throw new Error("invalid form data");
 
        }
         const existPost = await findPostBySlug(slug, locale);
        const existWallpaper: Partial<Wallpaper> = {
       
        if (existWallpaper&& existWallpaper.uuid !== wallpaper.uuid) {
          throw new Error("wallpaper with same slug already exists");
        }

        const updatedPost: Partial<Wallpaper> = {
        
          status,
          image_url,
          img_description,
        };

        try {
          await updatePost(wallpaper.uuid, updatedWallpaper);

          return {
            status: "success",
            message: "Wallpaper updated",
            redirect_url: "/admin/wallpaper",
          };
        } catch (err: any) {
          throw new Error(err.message);
        }
      },
    },
  };

  return <FormSlot {...form} />;
}
