'use client';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { useState } from "react";

export default function Generator() {
  const {user, setShowSignModal} = useAppContext();
  const [description, setDescription] = useState("");
    
  const requestGenWallpaper = async () => {
    try {
        toast.success("Generating wallpaper...");

        const resp = await fetch("/api/gen-wallpaper", {
            method: "POST",
            body: JSON.stringify({ description }),
        });

        if (!resp.ok) {
            throw new Error("Generate wallpaper failed");
        }

        const { code, message, wallpapers } = await resp.json();

        if (code !== 0) {
            throw new Error(message);
        }

        toast.success("Generate wallpaper success");
        window.location.reload();
    } catch (e) {
        toast.error("Generate wallpaper failed");
    }
};
  const handleGenerate = () => {
    if (!user) {
      toast.error("please login first");
      setShowSignModal(true);
      return;
    }
    if (!description.trim()) { 
      toast.error("Please enter a description");
      
      return
    }
    requestGenWallpaper();
  };

  return (
    <div className="flex w-full max-w-3xl mx-auto gap-4 mb-16 -mt-16">
      <Input
        value={description}
        onChange={(e) => setDescription(e.target.value)}
        placeholder="Wallpaper description"
        className="flex-1 rounded-full border-2 border-purple-500/20 bg-white/5 px-6 py-6 text-1g focus-vis"
      />
      <Button
        onClick={handleGenerate}
        className="rounded-full px-8 py-6 text-lg font-semibold transition-colors"
        style={{
          backgroundColor: 'hsl(var(--primary))',
          color: 'hsl(var(--primary-foreground))',
        }}
        onMouseOver={e => (e.currentTarget.style.backgroundColor = 'hsl(var(--primary) / 0.85)')}
        onMouseOut={e => (e.currentTarget.style.backgroundColor = 'hsl(var(--primary))')}
      >
        Generate
      </Button>
    </div>
  );
}