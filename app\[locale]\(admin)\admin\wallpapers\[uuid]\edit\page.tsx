import { findWallpaperByUuid, updateWallpaper } from "@/models/wallpaper";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";

interface PageProps {
  params: Promise<{ uuid: string }>;
}

export default async function EditWallpaperPage({ params }: PageProps) {
  const { uuid } = await params;
  const t = await getTranslations();
  
  const wallpaper = await findWallpaperByUuid(uuid);
  
  if (!wallpaper) {
    redirect("/admin/wallpapers");
  }

  async function updateWallpaperAction(formData: FormData) {
    "use server";
    
    const description = formData.get("description") as string;
    const status = formData.get("status") as string;
    
    if (!description || !status) {
      return;
    }
    
    try {
      await updateWallpaper(uuid, {
        img_description: description,
        status: status,
      });
      
      redirect("/admin/wallpapers");
    } catch (error) {
      console.error("Failed to update wallpaper:", error);
    }
  }

  return (
    <div className="container mx-auto p-6">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Edit Wallpaper</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <img 
              src={wallpaper.image_url} 
              alt={wallpaper.img_description}
              className="w-full max-w-md mx-auto rounded-lg shadow-sm"
            />
          </div>
          
          <form action={updateWallpaperAction} className="space-y-4">
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                defaultValue={wallpaper.img_description}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                id="status"
                name="status"
                defaultValue={wallpaper.status}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="created">Created</option>
                <option value="online">Online</option>
                <option value="offline">Offline</option>
              </select>
            </div>
            
            <div className="flex gap-4 pt-4">
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Update Wallpaper
              </button>
              
              <a
                href="/admin/wallpapers"
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
